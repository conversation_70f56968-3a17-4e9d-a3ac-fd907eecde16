# Modular System V2

This document describes the improved modular feature system implemented in the Flutter Common Package. The system allows for selective initialization of features based on the application's needs, improving startup time, reducing resource usage, and enhancing testability.

> **Important**: The Flutter Common Package is proprietary software owned by Trusting Social. All documentation should include appropriate proprietary notices and should not be shared outside of Trusting Social and its authorized partners and clients.

## Overview

The modular feature system is built around the concept of feature modules. Each module is responsible for registering a specific set of dependencies that are related to a particular feature or functionality. Modules can depend on other modules, and the system ensures that dependencies are initialized in the correct order.

The system provides comprehensive validation including circular dependency detection, module name uniqueness validation, and automatic dependency resolution.

## Key Components

### FeatureModule

The `FeatureModule` interface defines the contract for all feature modules. Each module must implement:

- `name`: A unique identifier for the module
- `dependencies`: A list of types that the module provides
- `register`: A method to register the module's dependencies

```dart
abstract class FeatureModule {
  String get name;
  List<Type> get dependencies;
  Future<void> register(GetIt getIt);
}
```

### ModuleRegistry and ModuleRegistryBuilder

The modular system uses a builder pattern for registering modules with comprehensive validation:

```dart
// IMPORTANT: Host applications should NOT use ModuleRegistry directly.
// Use the methods provided in init_common_package.dart instead.

// Internal example (for reference only):
final builder = ModuleRegistry.builder(GetIt.instance);
builder.register(CoreModule(), source: 'common_package')
       .register(NetworkModule(), source: 'common_package')
       .register(CustomModule(), source: 'host_app');

// Build the registry (validation happens automatically)
final registry = builder.build();

// Initialize modules
await registry.initializeAllRegisteredModules();
```

This approach provides several benefits:
- Clear separation between configuration and usage phases
- Automatic validation during the build phase (circular dependency detection, name uniqueness)
- Immutability after building
- Prevention of runtime errors
- Source tracking for better error messages

## Available Modules

The Flutter Common Package includes the following modules:

1. **CoreModule** (`common_core`): Essential dependencies like package info, secure storage, shared preferences, and UUID generation
2. **NetworkModule** (`common_network`): Networking dependencies like HTTP clients, Dio configuration, and connectivity management
3. **AnalyticsModule** (`common_analytics`): Analytics and logging dependencies including Firebase Analytics, Datadog, and crash reporting
4. **DeviceInfoModule** (`common_device_info`): Device-related information, identification, connectivity, and storage information
5. **DataCollectionModule** (`common_data_collection`): Data collection services that depend on device information
6. **NotificationModule** (`common_notification`): Push notification services using OneSignal
7. **UiModule** (`common_ui`): UI components and utilities like alert managers, image providers, and clipboard wrappers
8. **UtilityModule** (`common_utility`): Utility functions, downloaders, in-app review, and helper services
9. **EkycModule** (`common_ekyc`): Electronic Know Your Customer services

## Usage

### Basic Usage

To use the modular feature system, use the `features` parameter with `initCommonPackage`:

```dart
import 'package:flutter_common_package/base/module/module_names.dart';

// Initialize all modules (most common usage)
await initCommonPackage();

// Initialize only specific modules
await initCommonPackage(
  features: [
    CommonPackageModuleNames.core,
    CommonPackageModuleNames.network,
    CommonPackageModuleNames.ui,
  ],
);

// Initialize no modules (just register them for later initialization)
await initCommonPackage(features: []);

// Initialize with locale for localized network requests
await initCommonPackage(
  locale: Locale('en', 'US'),
  features: [CommonPackageModuleNames.network],
);
```

### Module Initialization

The modular system provides clear, explicit methods for initializing modules:

```dart
// Initialize all modules immediately (most common usage)
await initCommonPackage();

// Register modules without initializing them
await initCommonPackage(features: []);

// Later, initialize all registered modules
await initializeAllRegisteredModules();

// Initialize specific modules
await initializeSpecificModules([
  CommonPackageModuleNames.core,
  CommonPackageModuleNames.ui,
]);

// Explicitly initialize no modules (no-op)
await initializeNoModules();
```

The initialization API is designed to be clear and explicit:

- `initializeAllRegisteredModules()`: Initializes all registered modules
- `initializeSpecificModules(moduleNames)`: Initializes only the specified modules
- `initializeNoModules()`: Explicitly does nothing (useful for conditional initialization)

This approach makes the code more readable and self-documenting, as the intent is clear from the method name.

When using `initCommonPackage`:
- Use the `features` parameter to specify which modules to register and initialize
- If `features` is null, all modules will be registered and initialized
- If `features` is empty, all modules will be registered but none will be initialized
- If `features` contains specific module names, only those modules (and their dependencies) will be initialized

The initialization process respects module dependencies, ensuring that if module A depends on module B, module B will be initialized first.

### Creating Custom Modules

You can create custom modules for your application by implementing the `FeatureModule` interface:

```dart
class CustomAuthModule implements FeatureModule {
  @override
  String get name => 'auth';

  @override
  List<Type> get dependencies => [AuthService];

  @override
  Future<void> register(GetIt getIt) async {
    getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl());
  }
}
```

Then register your custom module using the provided API methods:

```dart
// First, initialize the common package
await initCommonPackage();

// Register your custom module
registerCustomModule(CustomAuthModule(), source: 'host_app');

// Initialize it
await initializeSpecificModules(['auth']);

// Or register and initialize in one step
await registerAndInitializeCustomModule(CustomAuthModule(), source: 'host_app');
```

### Module Name Uniqueness

The `source` parameter is required when registering modules and helps ensure module name uniqueness across different sources (e.g., 'common_package', 'host_app'). This prevents naming conflicts when modules from different sources use the same name.

The system automatically validates module names during registration:

```dart
// Register modules from different sources
registerCustomModule(CoreModule(), source: 'common_package');
registerCustomModule(CustomAuthModule(), source: 'host_app');

// Validation happens automatically during registration
```

## Validation and Error Handling

### Comprehensive Validation

The modular system includes comprehensive validation that occurs automatically:

1. **Module Name Validation**: Ensures module names are unique across sources
2. **Circular Dependency Detection**: Detects and prevents circular dependencies between modules
3. **Source Tracking**: Tracks where each module comes from for better error messages
4. **Type Safety**: Validates module types and dependencies

### Circular Dependency Detection

The system automatically detects circular dependencies during module registration and building:

```dart
// This will throw an exception if there's a circular dependency
await initCommonPackage();
```

To avoid circular dependencies, consider these approaches:

1. **Restructure module boundaries**: Ensure clear separation of concerns
2. **Use dependency injection**: Inject dependencies rather than creating them directly
3. **Review module dependencies**: Ensure dependencies flow in one direction

## API Reference

### Core Methods

#### `initCommonPackage({Locale? locale, List<String>? features})`
Initializes the common package with all or selected features.

- `locale`: Used for localization in HTTP requests
- `features`: Specifies which modules to initialize
  - `null`: Initialize all modules (default)
  - `[]`: Register all modules but initialize none
  - `[module_names]`: Initialize only specified modules

#### Module Query Methods

- `getRegisteredModules()`: Returns list of all registered module names
- `getInitializedModules()`: Returns list of all initialized module names
- `isModuleRegistered(String moduleName)`: Checks if a module is registered
- `isModuleInitialized(String moduleName)`: Checks if a module is initialized

#### Module Initialization Methods

- `initializeAllRegisteredModules()`: Initializes all registered modules
- `initializeSpecificModules(List<String> moduleNames)`: Initializes specific modules
- `initializeNoModules()`: No-op method for explicit clarity

#### Custom Module Methods

- `registerCustomModule(FeatureModule module, {required String source})`: Registers a custom module
- `registerAndInitializeCustomModule(FeatureModule module, {required String source})`: Registers and initializes a custom module

## Benefits

The modular feature system provides several benefits:

1. **Reduced Startup Time**: Initialize only the features your app needs
2. **Lower Resource Usage**: Avoid loading unnecessary dependencies
3. **Improved Testability**: Easily mock or replace modules for testing
4. **Better Dependency Management**: Clear dependencies between modules with automatic resolution
5. **Easier Extension**: Add new features without modifying existing code
6. **Comprehensive Validation**: Catches configuration errors early with detailed error messages
7. **Source Tracking**: Clear identification of where modules come from
8. **Circular Dependency Prevention**: Automatic detection and prevention of dependency cycles
9. **Performance Monitoring**: Built-in performance tracking for module initialization

## Testing and Debugging

### Testing Utilities

The modular system includes comprehensive testing utilities to help with unit testing:

```dart
import 'package:flutter_common_package/test/base/module/module_test_utils.dart';

// Set up a test environment with mocked dependencies
final registry = ModuleTestUtils.setupTestEnvironment(getIt);

// Create test modules for testing
final testModule = ModuleTestUtils.createTestModule(
  name: 'test_module',
  dependencies: [TestService],
  registerFn: (getIt) async {
    getIt.registerSingleton<TestService>(MockTestService());
  },
);
```

### Debugging Methods

For debugging and monitoring module state:

```dart
// Check module status
final registeredModules = getRegisteredModules();
final initializedModules = getInitializedModules();

print('Registered: $registeredModules');
print('Initialized: $initializedModules');

// Check specific module status
if (isModuleRegistered('custom_module')) {
  print('Module is registered');
}

if (isModuleInitialized('custom_module')) {
  print('Module is initialized');
}
```

### Performance Testing

The system includes performance testing utilities to measure initialization times:

```dart
// Performance tests are available in the test suite
// See: test/base/module/module_init_performance_comparison_test.dart
```

## Best Practices

1. **Use the provided API**: Always use methods in `init_common_package.dart` instead of accessing `ModuleRegistry` directly
2. **Specify source clearly**: Use descriptive source names like 'host_app', 'common_package', 'plugin_name'
3. **Keep modules focused**: Each module should have a single, clear responsibility
4. **Minimize dependencies**: Reduce coupling between modules where possible
5. **Test module isolation**: Ensure modules can be tested independently using the provided test utilities
6. **Document custom modules**: Provide clear documentation for any custom modules
7. **Monitor performance**: Use the debugging methods to monitor module initialization performance
8. **Use mock modules in tests**: Leverage the provided mock modules for consistent testing

## Conclusion

The improved modular system provides a robust, maintainable, and error-resistant approach to managing dependencies in your application. With comprehensive validation, automatic dependency resolution, and clear APIs, it enables you to create applications that are easier to develop, test, and maintain.
