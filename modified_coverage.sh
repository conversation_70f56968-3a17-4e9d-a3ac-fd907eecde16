#!/bin/bash

# Script to add proprietary notice to Dart files
# Usage: ./add_proprietary_notice.sh

NOTICE="// Copyright (c) 2024 Trusting Social. All rights reserved.\n// Proprietary and confidential. Unauthorized copying or distribution is prohibited.\n"

# Find all Dart files in the lib directory
find lib -name "*.dart" | while read -r file; do
  # Check if the file already has the notice
  if ! grep -q "Copyright (c) 2024 Trusting Social" "$file"; then
    echo "Adding notice to $file"
    # Create a temporary file
    temp_file=$(mktemp)
    # Add the notice and then the original content
    echo -e "$NOTICE\n$(cat "$file")" > "$temp_file"
    # Replace the original file with the temporary file
    mv "$temp_file" "$file"
  else
    echo "Notice already exists in $file"
  fi
done

echo "Completed adding proprietary notices to Dart files."
