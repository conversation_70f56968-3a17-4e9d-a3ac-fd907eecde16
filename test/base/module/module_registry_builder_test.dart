// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_common_package/base/module/module_registry_builder.dart';
import 'package:flutter_common_package/base/module/module_registration_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';

import 'module_test_utils.dart';

void main() {
  group('ModuleRegistryBuilder', () {
    late GetIt getIt;
    late ModuleRegistryBuilder builder;

    setUp(() {
      getIt = GetIt.instance;
      getIt.reset();
      builder = ModuleRegistryBuilder(getIt);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Constructor and Basic Properties', () {
      test('should initialize with correct GetIt instance', () {
        expect(builder.getIt, equals(getIt));
        expect(builder.isBuilt, isFalse);
        expect(builder.moduleRegistrations, isEmpty);
      });

      test('should start with empty module registrations', () {
        expect(builder.moduleRegistrations, isEmpty);
        expect(builder.isBuilt, isFalse);
      });
    });

    group('register method', () {
      test('should register a module successfully', () {
        final module = ModuleTestUtils.createTestModule(name: 'TestModule');
        
        final result = builder.register(module, source: 'test');
        
        expect(result, equals(builder)); // Should return builder for chaining
        expect(builder.moduleRegistrations, hasLength(1));
        expect(builder.moduleRegistrations['TestModule'], isNotNull);
        expect(builder.moduleRegistrations['TestModule']!.module, equals(module));
        expect(builder.moduleRegistrations['TestModule']!.source, equals('test'));
      });

      test('should allow method chaining', () {
        final module1 = ModuleTestUtils.createTestModule(name: 'Module1');
        final module2 = ModuleTestUtils.createTestModule(name: 'Module2');
        
        final result = builder
            .register(module1, source: 'test1')
            .register(module2, source: 'test2');
        
        expect(result, equals(builder));
        expect(builder.moduleRegistrations, hasLength(2));
      });

      test('should throw StateError when registering after build', () {
        final module = ModuleTestUtils.createTestModule(name: 'TestModule');
        builder.register(module, source: 'test');
        builder.build();
        
        final newModule = ModuleTestUtils.createTestModule(name: 'NewModule');
        expect(
          () => builder.register(newModule, source: 'test'),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            contains('Cannot register modules after the registry is built'),
          )),
        );
      });

      test('should replace module with same name and type', () {
        final module1 = ModuleTestUtils.createTestModule(name: 'TestModule');
        final module2 = ModuleTestUtils.createTestModule(name: 'TestModule');
        
        builder.register(module1, source: 'test1');
        builder.register(module2, source: 'test2');
        
        expect(builder.moduleRegistrations, hasLength(1));
        expect(builder.moduleRegistrations['TestModule']!.module, equals(module2));
        expect(builder.moduleRegistrations['TestModule']!.source, equals('test2'));
      });
    });

    group('detectCycle method', () {
      test('should return false for acyclic graph', () {
        final graph = <String, Set<String>>{
          'A': <String>{'B'},
          'B': <String>{'C'},
          'C': <String>{},
        };
        final visited = <String>{};
        final recursionStack = <String>{};
        
        final result = builder.detectCycle('A', graph, visited, recursionStack);
        
        expect(result, isFalse);
      });

      test('should return true for cyclic graph', () {
        final graph = <String, Set<String>>{
          'A': <String>{'B'},
          'B': <String>{'C'},
          'C': <String>{'A'},
        };
        final visited = <String>{};
        final recursionStack = <String>{};
        
        final result = builder.detectCycle('A', graph, visited, recursionStack);
        
        expect(result, isTrue);
      });

      test('should handle self-referencing cycle', () {
        final graph = <String, Set<String>>{
          'A': <String>{'A'},
        };
        final visited = <String>{};
        final recursionStack = <String>{};
        
        final result = builder.detectCycle('A', graph, visited, recursionStack);
        
        expect(result, isTrue);
      });

      test('should handle complex graph with no cycles', () {
        final graph = <String, Set<String>>{
          'A': <String>{'B', 'C'},
          'B': <String>{'D'},
          'C': <String>{'D'},
          'D': <String>{},
        };
        final visited = <String>{};
        final recursionStack = <String>{};
        
        final result = builder.detectCycle('A', graph, visited, recursionStack);
        
        expect(result, isFalse);
      });
    });

    group('build method', () {
      test('should build registry successfully with valid modules', () {
        final module = ModuleTestUtils.createTestModule(name: 'TestModule');
        builder.register(module, source: 'test');
        
        final registry = builder.build();
        
        expect(registry, isA<ModuleRegistry>());
        expect(builder.isBuilt, isTrue);
      });

      test('should throw StateError when building twice', () {
        final module = ModuleTestUtils.createTestModule(name: 'TestModule');
        builder.register(module, source: 'test');
        builder.build();
        
        expect(
          () => builder.build(),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            contains('Registry is already built'),
          )),
        );
      });

      test('should build empty registry successfully', () {
        final registry = builder.build();

        expect(registry, isA<ModuleRegistry>());
        expect(builder.isBuilt, isTrue);
      });
    });

    group('Complex Scenarios', () {
      test('should handle multiple modules with complex dependencies', () {
        final coreModule = ModuleTestUtils.createTestModule(
          name: 'CoreModule',
          dependencies: [],
        );
        final networkModule = ModuleTestUtils.createTestModule(
          name: 'NetworkModule',
          dependencies: [String], // Depends on core
        );
        final analyticsModule = ModuleTestUtils.createTestModule(
          name: 'AnalyticsModule',
          dependencies: [int, double], // Depends on network and core
        );

        builder
            .register(coreModule, source: 'common_package')
            .register(networkModule, source: 'common_package')
            .register(analyticsModule, source: 'host_app');

        final registry = builder.build();

        expect(registry, isA<ModuleRegistry>());
        expect(builder.moduleRegistrations, hasLength(3));
      });

      test('should handle module registration with empty dependencies gracefully', () {
        final module = _ModuleWithNullDependencies();

        expect(
          () => builder.register(module, source: 'test'),
          returnsNormally,
        );

        expect(
          () => builder.build(),
          returnsNormally,
        );
      });

      test('should preserve registration order and metadata', () {
        final module1 = ModuleTestUtils.createTestModule(name: 'First');
        final module2 = ModuleTestUtils.createTestModule(name: 'Second');

        final beforeRegistration = DateTime.now();

        builder
            .register(module1, source: 'source1')
            .register(module2, source: 'source2');

        final afterRegistration = DateTime.now();

        final registrations = builder.moduleRegistrations;
        expect(registrations, hasLength(2));

        final firstReg = registrations['First']!;
        final secondReg = registrations['Second']!;

        expect(firstReg.source, equals('source1'));
        expect(secondReg.source, equals('source2'));
        expect(firstReg.registeredAt.isAfter(beforeRegistration), isTrue);
        expect(firstReg.registeredAt.isBefore(afterRegistration), isTrue);
        expect(secondReg.registeredAt.isAfter(firstReg.registeredAt), isTrue);
      });


    });


  });
}

/// Test module with empty dependencies (simulating null behavior)
class _ModuleWithNullDependencies implements FeatureModule {
  @override
  String get name => 'NullDepsModule';

  @override
  List<Type> get dependencies => [];

  @override
  Future<void> register(GetIt getIt) async {}
}
